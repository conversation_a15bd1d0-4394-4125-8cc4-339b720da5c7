import type { Metada<PERSON> } from 'next'
import { GeistSans } from 'geist/font/sans'
import { GeistMono } from 'geist/font/mono'
import { PerformanceMonitor } from '@/components/performance-monitor'
import './globals.css'

export const metadata: Metadata = {
  title: 'Chiikawa Puzzle Game | Free Online Kawaii Brain Teasers & Challenges',
  description: 'Play free Chiikawa puzzle games featuring adorable Japanese characters. Enjoy brain training puzzles, progressive difficulty levels, and family-friendly entertainment. No download required!',
  generator: 'Chiikawa Puzzle',
  keywords: 'Chiikawa puzzle game, kawaii puzzles, brain training games, Japanese character puzzles, free online puzzles, Chiikawa characters, family-friendly games',
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <head>
        <style>{`
html {
  font-family: ${GeistSans.style.fontFamily};
  --font-sans: ${GeistSans.variable};
  --font-mono: ${GeistMono.variable};
}
        `}</style>
      </head>
      <body>
        <PerformanceMonitor />
        {children}
      </body>
    </html>
  )
}
