'use client';

import { useEffect, useRef } from 'react';
import * as PIXI from 'pixi.js';

// Game constants and types
interface PuzzlePart {
  name: string;
  textureUrl: string;
}

const PUZZLE_PARTS: PuzzlePart[] = [
  { name: 'Face', textureUrl: '/assets/images/chiikawa_face.png' },
  { name: 'Left Ear', textureUrl: '/assets/images/chiikawa_left_ear.png' },
  { name: 'Right Ear', textureUrl: '/assets/images/chiikawa_right_ear.png' },
  { name: 'Left Eye', textureUrl: '/assets/images/chiikawa_left_eye.png' },
  { name: 'Right Eye', textureUrl: '/assets/images/chiikawa_right_eye.png' },
  { name: 'Left Eyebrow', textureUrl: '/assets/images/chiikawa_left_eyebrow.png' },
  { name: 'Right Eyebrow', textureUrl: '/assets/images/chiikawa_right_eyebrow.png' },
  { name: 'Nose', textureUrl: '/assets/images/chiikawa_nose.png' },
  { name: 'Mouth', textureUrl: '/assets/images/chiikawa_mouth.png' },
  { name: 'Left Cheek', textureUrl: '/assets/images/chiikawa_left_cheek.png' },
  { name: 'Right Cheek', textureUrl: '/assets/images/chiikawa_right_cheek.png' },
  { name: 'Body', textureUrl: '/assets/images/chiikawa_body_new.png' },
  { name: 'Left Hand', textureUrl: '/assets/images/chiikawa_left_hand.png' },
  { name: 'Right Hand', textureUrl: '/assets/images/chiikawa_right_hand.png' },
  { name: 'Left Leg', textureUrl: '/assets/images/chiikawa_left_leg.png' },
  { name: 'Right Leg', textureUrl: '/assets/images/chiikawa_right_leg.png' }
];

// Asset Manager Class
class AssetManager {
  private assets = new Map();
  private isLoading = false;

  async preloadAssets(assetList: string[]) {
    this.isLoading = true;
    
    try {
      const loadPromises = assetList.map(async (asset) => {
        return await PIXI.Assets.load(asset);
      });

      await Promise.all(loadPromises);
      console.log('All assets loaded successfully');
    } catch (error) {
      console.warn('Some assets failed to load:', error);
    } finally {
      this.isLoading = false;
    }
  }

  getAsset(name: string) {
    return this.assets.get(name) || PIXI.Texture.from(name);
  }

  isLoadingAssets() {
    return this.isLoading;
  }
}

// Game State Manager
class GameStateManager {
  private currentState = 'LOADING';
  private callbacks = new Map<string, () => void>();
  private memoryTime = 5000;
  private startTime = 0;
  private placedParts = new Set<string>();

  setState(newState: string) {
    this.currentState = newState;
    const callback = this.callbacks.get(newState);
    if (callback) callback();
  }

  onStateChange(state: string, callback: () => void) {
    this.callbacks.set(state, callback);
  }

  getState() {
    return this.currentState;
  }

  startTimer() {
    this.startTime = Date.now();
  }

  getElapsedTime() {
    return Date.now() - this.startTime;
  }

  addPlacedPart(partName: string) {
    this.placedParts.add(partName);
  }

  isPartPlaced(partName: string) {
    return this.placedParts.has(partName);
  }

  getAllPlacedParts() {
    return Array.from(this.placedParts);
  }

  resetPlacedParts() {
    this.placedParts.clear();
  }

  isGameComplete() {
    return this.placedParts.size === PUZZLE_PARTS.length;
  }
}

// UI Manager
class UIManager {
  private app: PIXI.Application;
  private uiElements: PIXI.DisplayObject[] = [];

  constructor(app: PIXI.Application) {
    this.app = app;
  }

  createText(text: string, style: Partial<PIXI.TextStyle> = {}) {
    const defaultStyle = {
      fontFamily: 'Fredoka One, Arial, sans-serif',
      fontSize: 18,
      fill: 0x333333,
      align: 'center',
      wordWrap: true,
      wordWrapWidth: 500
    };
    
    return new PIXI.Text(text, { ...defaultStyle, ...style });
  }

  createButton(text: string, style: Partial<PIXI.TextStyle> = {}) {
    const buttonStyle = {
      fontFamily: 'Fredoka One, Arial, sans-serif',
      fontSize: 14,
      fill: 0xFFFFFF,
      align: 'center',
      wordWrap: true,
      wordWrapWidth: 120
    };

    const buttonText = new PIXI.Text(text, { ...buttonStyle, ...style });
    const button = new PIXI.Graphics();
    
    // Ensure minimum button width and proper padding
    const minWidth = 100;
    const buttonWidth = Math.max(minWidth, buttonText.width + 30);
    const buttonHeight = buttonText.height + 16;
    
    button.beginFill(0x4CAF50);
    button.drawRoundedRect(-buttonWidth/2, -buttonHeight/2, buttonWidth, buttonHeight, 8);
    button.endFill();
    
    // Center the text within the button
    buttonText.anchor.set(0.5);
    buttonText.x = 0;
    buttonText.y = 0;
    
    button.addChild(buttonText);
    button.interactive = true;
    button.buttonMode = true;
    
    return button;
  }

  addToUI(element: PIXI.DisplayObject) {
    this.uiElements.push(element);
    this.app.stage.addChild(element);
  }

  clearUI() {
    this.uiElements.forEach(element => {
      if (element.parent) {
        element.parent.removeChild(element);
      }
    });
    this.uiElements = [];
  }
}

// Main Game Class
class ChiikawaGame {
  private app: PIXI.Application | null = null;
  private gameState = new GameStateManager();
  private ui: UIManager | null = null;
  private assetManager = new AssetManager();
  private puzzleContainer = new PIXI.Container();
  private cursorSprite: PIXI.Sprite | null = null;
  
  // UI elements
  private loadingText: PIXI.Text | null = null;
  private titleText: PIXI.Text | null = null;
  private startButton: PIXI.Graphics | null = null;
  private memoryImage: PIXI.Sprite | null = null;
  private timerText: PIXI.Text | null = null;
  private instructionText: PIXI.Text | null = null;
  private playAgainButton: PIXI.Graphics | null = null;
  private shareButton: PIXI.Graphics | null = null;

  async init(container: HTMLElement) {
    // Initialize PixiJS application
    this.app = new PIXI.Application();
    await this.app.init({
      width: 600,
      height: 600,
      backgroundColor: 0xFFFFFF,
      antialias: true,
      autoDensity: true,
      resolution: window.devicePixelRatio || 1
    });

    container.appendChild(this.app.canvas as HTMLCanvasElement);

    // Initialize modules
    this.ui = new UIManager(this.app);
    this.app.stage.addChild(this.puzzleContainer);

    // Set background
    this.setBackground();

    // Set up state callbacks
    this.setupStateCallbacks();

    // Start the game
    this.showLoadingScreen();
    await this.loadAssets();
    this.gameState.setState('START_SCREEN');
  }

  private setupStateCallbacks() {
    this.gameState.onStateChange('START_SCREEN', () => this.showStartScreen());
    this.gameState.onStateChange('MEMORY', () => this.showMemoryPhase());
    this.gameState.onStateChange('PUZZLING', () => this.startPuzzlingPhase());
    this.gameState.onStateChange('REVEAL', () => this.showRevealPhase());
  }

  private setBackground() {
    if (!this.app) return;
    
    const background = new PIXI.Graphics();
    background.beginFill(0xd5ecbf);
    background.drawRect(0, 0, this.app.screen.width, this.app.screen.height);
    background.endFill();
    this.app.stage.addChildAt(background, 0);
  }

  private showLoadingScreen() {
    if (!this.ui || !this.app) return;
    
    this.loadingText = this.ui.createText('Loading...', { fontSize: 24 });
    this.loadingText.anchor.set(0.5);
    this.loadingText.x = this.app.screen.width / 2;
    this.loadingText.y = this.app.screen.height / 2;
    this.ui.addToUI(this.loadingText);
  }

  private async loadAssets() {
    const assetsToLoad = [
      '/assets/images/chiikawa_full.png',
      ...PUZZLE_PARTS.map(part => part.textureUrl)
    ];

    await this.assetManager.preloadAssets(assetsToLoad);
  }

  private showStartScreen() {
    if (!this.ui || !this.app) return;
    
    this.ui.clearUI();

    this.titleText = this.ui.createText('Chiikawa Memory Puzzle', { 
      fontSize: 28, 
      fill: 0x2E7D32,
      wordWrapWidth: this.app.screen.width - 40
    });
    this.titleText.anchor.set(0.5);
    this.titleText.x = this.app.screen.width / 2;
    this.titleText.y = 150;
    this.ui.addToUI(this.titleText);

    this.startButton = this.ui.createButton('Start Game');
    this.startButton.x = this.app.screen.width / 2;
    this.startButton.y = 300;
    this.startButton.on('pointerdown', () => {
      this.gameState.setState('MEMORY');
    });
    this.ui.addToUI(this.startButton);
  }

  private showMemoryPhase() {
    if (!this.ui || !this.app) return;
    
    this.ui.clearUI();

    const memoryTexture = PIXI.Texture.from('/assets/images/chiikawa_full.png');
    this.memoryImage = new PIXI.Sprite(memoryTexture);
    this.memoryImage.anchor.set(0.5);
    this.memoryImage.x = this.app.screen.width / 2;
    this.memoryImage.y = this.app.screen.height / 2;
    
    const scale = Math.min(400 / this.memoryImage.width, 400 / this.memoryImage.height);
    this.memoryImage.scale.set(scale);
    
    this.ui.addToUI(this.memoryImage);

    // Add countdown timer display
    let countdown = 5;
    this.timerText = this.ui.createText(`Memorize This! ${countdown}`, {
      fontSize: 32,
      fill: 0xFF4444,
      fontWeight: 'bold'
    });
    this.timerText.anchor.set(0.5);
    this.timerText.x = this.app.screen.width / 2;
    this.timerText.y = 100;
    this.ui.addToUI(this.timerText);

    // Start countdown
    const countdownInterval = setInterval(() => {
      countdown--;
      if (this.timerText) {
         this.timerText.text = `Memorize This! ${countdown}`;
        
        // Change color as time runs out
        if (countdown <= 2) {
          this.timerText.style.fill = 0xFF0000; // Red
        } else if (countdown <= 3) {
          this.timerText.style.fill = 0xFF8800; // Orange
        }
      }
      
      if (countdown <= 0) {
        clearInterval(countdownInterval);
        this.gameState.setState('PUZZLING');
      }
    }, 1000);
  }

  private startPuzzlingPhase() {
    if (!this.ui || !this.app) return;
    
    this.ui.clearUI();
    this.gameState.startTimer();
    this.gameState.resetPlacedParts();

    this.instructionText = this.ui.createText('Click to place puzzle pieces!', { fontSize: 18 });
    this.instructionText.anchor.set(0.5);
    this.instructionText.x = this.app.screen.width / 2;
    this.instructionText.y = 50;
    this.ui.addToUI(this.instructionText);

    this.setupPuzzleInteraction();
  }

  private setupPuzzleInteraction() {
    if (!this.app) return;
    
    this.app.canvas.style.cursor = 'crosshair';
    
    const onCanvasClick = (event: PointerEvent) => {
      if (this.gameState.getState() !== 'PUZZLING') return;
      
      const rect = (this.app!.canvas as HTMLCanvasElement).getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;
      
      this.placePuzzlePiece(x, y);
    };
    
    this.app.canvas.addEventListener('pointerdown', onCanvasClick);
  }

  private placePuzzlePiece(x: number, y: number) {
    const availableParts = PUZZLE_PARTS.filter(part => !this.gameState.isPartPlaced(part.name));
    
    if (availableParts.length === 0) return;
    
    const randomPart = availableParts[Math.floor(Math.random() * availableParts.length)];
    const texture = PIXI.Texture.from(randomPart.textureUrl);
    const sprite = new PIXI.Sprite(texture);
    
    sprite.anchor.set(0.5);
    sprite.x = x;
    sprite.y = y;
    sprite.scale.set(0.8);
    
    this.puzzleContainer.addChild(sprite);
    this.gameState.addPlacedPart(randomPart.name);
    
    if (this.gameState.isGameComplete()) {
      this.gameState.setState('REVEAL');
    }
  }

  private showRevealPhase() {
    if (!this.ui || !this.app) return;
    
    this.ui.clearUI();
    
    // Show the completed puzzle
    this.puzzleContainer.children.forEach(child => {
      child.alpha = 1;
    });
    
    const completionText = this.ui.createText('Your Masterpiece!', { 
      fontSize: 28, 
      fill: 0x4CAF50,
      wordWrapWidth: this.app.screen.width - 40
    });
    completionText.anchor.set(0.5);
    completionText.x = this.app.screen.width / 2;
    completionText.y = 50;
    this.ui.addToUI(completionText);
    
    const timeText = this.ui.createText(`Completed in: ${Math.round(this.gameState.getElapsedTime() / 1000)}s`, { fontSize: 18 });
    timeText.anchor.set(0.5);
    timeText.x = this.app.screen.width / 2;
    timeText.y = 100;
    this.ui.addToUI(timeText);
    
    // Reset button (left side)
    this.playAgainButton = this.ui.createButton('🔄 Play Again');
    this.playAgainButton.x = this.app.screen.width / 2 - 90;
    this.playAgainButton.y = this.app.screen.height - 80;
    this.playAgainButton.on('pointerdown', () => {
      this.resetGame();
    });
    this.ui.addToUI(this.playAgainButton);
    
    // Share button (right side)
    this.shareButton = this.ui.createButton('📷 Save Image');
    this.shareButton.x = this.app.screen.width / 2 + 90;
    this.shareButton.y = this.app.screen.height - 80;
    this.shareButton.on('pointerdown', () => {
      this.shareCreation();
    });
    this.ui.addToUI(this.shareButton);
  }

  private resetGame() {
    this.puzzleContainer.removeChildren();
    this.gameState.resetPlacedParts();
    this.gameState.setState('START_SCREEN');
  }

  private shareCreation() {
    if (!this.app) return;
    
    try {
      // Create a temporary container with background for export
      const exportContainer = new PIXI.Container();
      
      // Add background
      const background = new PIXI.Graphics();
      background.beginFill(0xd5ecbf);
      background.drawRect(0, 0, 600, 600);
      background.endFill();
      exportContainer.addChild(background);
      
      // Copy all puzzle pieces to export container
      this.puzzleContainer.children.forEach(child => {
        if (child instanceof PIXI.Sprite) {
          const copy = new PIXI.Sprite(child.texture);
          copy.x = child.x;
          copy.y = child.y;
          copy.scale.copyFrom(child.scale);
          copy.rotation = child.rotation;
          copy.anchor.copyFrom(child.anchor);
          copy.alpha = child.alpha;
          exportContainer.addChild(copy);
        }
      });
      
      // Add watermark text
       const watermarkText = new PIXI.Text('BY: https://www.chiikawapuzzle.cc/', {
         fontFamily: 'Arial',
         fontSize: 20,
         fontWeight: 'bold',
         fill: 0xffffff,
         stroke: 0x000000,
         strokeThickness: 2,
         align: 'center'
       });
      watermarkText.anchor.set(0.5);
      watermarkText.x = 300;
      watermarkText.y = 570;
      exportContainer.addChild(watermarkText);
      
      // Add timestamp
      const timestampText = new PIXI.Text(new Date().toLocaleDateString(), {
        fontFamily: 'Arial',
        fontSize: 16,
        fill: 0xffffff,
        stroke: 0x000000,
        strokeThickness: 1,
        align: 'center'
      });
      timestampText.anchor.set(0.5);
      timestampText.x = 300;
      timestampText.y = 595;
      exportContainer.addChild(timestampText);
      
      // Extract the container as canvas and download
      const canvas = this.app.renderer.extract.canvas(exportContainer);
      const link = document.createElement('a');
      link.download = `chiikawa-puzzle-${Date.now()}.png`;
      link.href = canvas.toDataURL('image/png', 1.0);
      link.click();
      
      // Clean up
      exportContainer.destroy({ children: true });
      
    } catch (error) {
      console.error('Failed to save image:', error);
      alert('Sorry, could not save the image. Please try again!');
    }
  }

  destroy() {
    if (this.app) {
      this.app.destroy(true);
      this.app = null;
    }
  }
}

export default function ChiikawaGameComponent() {
  const gameRef = useRef<HTMLDivElement>(null);
  const gameInstanceRef = useRef<ChiikawaGame | null>(null);

  useEffect(() => {
    if (!gameRef.current) return;

    const game = new ChiikawaGame();
    gameInstanceRef.current = game;
    
    game.init(gameRef.current);

    return () => {
      game.destroy();
    };
  }, []);

  return (
    <div className="w-full flex justify-center items-center min-h-[600px] bg-gray-50">
      <div 
        ref={gameRef} 
        className="border-2 border-gray-200 rounded-lg shadow-lg bg-white"
        style={{ width: '600px', height: '600px' }}
      />
    </div>
  );
}